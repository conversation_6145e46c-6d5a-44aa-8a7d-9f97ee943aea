'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useUpdateCtaSection } from '@/hooks/useMutation';
import { ICtaSection } from '@/types/query.types';

interface CtaSectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  data?: ICtaSection;
}

interface FormData {
  heading: string;
  description: string;
  buttonText: string;
  buttonLink: string;
  backgroundImage: string;
}

export function CtaSectionModal({
  isOpen,
  onClose,
  data
}: CtaSectionModalProps) {
  const [formData, setFormData] = useState<FormData>({
    heading: '',
    description: '',
    buttonText: '',
    buttonLink: '',
    backgroundImage: ''
  });

  const { mutate: updateSection, isPending } = useUpdateCtaSection({
    onSuccess: () => {
      onClose();
    }
  });

  useEffect(() => {
    if (data) {
      setFormData({
        heading: data.heading || '',
        description: data.description || '',
        buttonText: data.buttonText || '',
        buttonLink: data.buttonLink || '',
        backgroundImage: data.backgroundImage || ''
      });
    }
  }, [data]);

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.heading.trim() || !formData.description.trim()) {
      return;
    }

    updateSection({
      heading: formData.heading.trim(),
      description: formData.description.trim(),
      buttonText: formData.buttonText.trim(),
      buttonLink: formData.buttonLink.trim(),
      backgroundImage: formData.backgroundImage.trim()
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-h-[90vh] max-w-2xl'>
        <DialogHeader>
          <DialogTitle>Edit Call to Action Section</DialogTitle>
          <DialogDescription>
            Update the CTA section content including heading, description,
            button, and background image.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <ScrollArea className='max-h-[60vh] pr-4'>
            <div className='space-y-6'>
              <div className='space-y-2'>
                <Label htmlFor='heading'>Heading *</Label>
                <Input
                  id='heading'
                  value={formData.heading}
                  onChange={(e) => handleInputChange('heading', e.target.value)}
                  placeholder='Enter section heading'
                  required
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='description'>Description *</Label>
                <Textarea
                  id='description'
                  value={formData.description}
                  onChange={(e) =>
                    handleInputChange('description', e.target.value)
                  }
                  placeholder='Enter section description'
                  rows={4}
                  required
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='backgroundImage'>Background Image URL</Label>
                <Input
                  id='backgroundImage'
                  value={formData.backgroundImage}
                  onChange={(e) =>
                    handleInputChange('backgroundImage', e.target.value)
                  }
                  placeholder='Enter background image URL'
                  type='url'
                />
              </div>

              <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                <div className='space-y-2'>
                  <Label htmlFor='buttonText'>Button Text</Label>
                  <Input
                    id='buttonText'
                    value={formData.buttonText}
                    onChange={(e) =>
                      handleInputChange('buttonText', e.target.value)
                    }
                    placeholder='Enter button text'
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='buttonLink'>Button Link</Label>
                  <Input
                    id='buttonLink'
                    value={formData.buttonLink}
                    onChange={(e) =>
                      handleInputChange('buttonLink', e.target.value)
                    }
                    placeholder='Enter button link URL'
                    type='url'
                  />
                </div>
              </div>
            </div>
          </ScrollArea>

          <DialogFooter className='mt-6'>
            <Button
              type='button'
              variant='outline'
              onClick={onClose}
              disabled={isPending}
            >
              Cancel
            </Button>
            <Button
              type='submit'
              disabled={
                isPending ||
                !formData.heading.trim() ||
                !formData.description.trim()
              }
            >
              {isPending ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
