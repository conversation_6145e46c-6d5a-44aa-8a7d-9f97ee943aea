'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useUpdateDiscoverSection } from '@/hooks/useMutation';
import { IDiscoverSection } from '@/types/query.types';

interface DiscoverSectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  data?: IDiscoverSection;
}

interface FormData {
  heading: string;
  description: string;
  image: string;
  buttonText: string;
  buttonLink: string;
}

export function DiscoverSectionModal({
  isOpen,
  onClose,
  data
}: DiscoverSectionModalProps) {
  const [formData, setFormData] = useState<FormData>({
    heading: '',
    description: '',
    image: '',
    buttonText: '',
    buttonLink: ''
  });

  const { mutate: updateSection, isPending } = useUpdateDiscoverSection({
    onSuccess: () => {
      onClose();
    }
  });

  useEffect(() => {
    if (data) {
      setFormData({
        heading: data.heading || '',
        description: data.description || '',
        image: data.image || '',
        buttonText: data.buttonText || '',
        buttonLink: data.buttonLink || ''
      });
    }
  }, [data]);

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.heading.trim() || !formData.description.trim()) {
      return;
    }

    updateSection({
      heading: formData.heading.trim(),
      description: formData.description.trim(),
      image: formData.image.trim(),
      buttonText: formData.buttonText.trim(),
      buttonLink: formData.buttonLink.trim()
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-h-[90vh] max-w-2xl'>
        <DialogHeader>
          <DialogTitle>Edit Discover Section</DialogTitle>
          <DialogDescription>
            Update the discover section content including heading, description,
            image, and call-to-action button.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <ScrollArea className='max-h-[60vh] pr-4'>
            <div className='space-y-6'>
              <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                <div className='space-y-2'>
                  <Label htmlFor='heading'>Heading *</Label>
                  <Input
                    id='heading'
                    value={formData.heading}
                    onChange={(e) => handleInputChange('heading', e.target.value)}
                    placeholder='Enter section heading'
                    required
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='image'>Image URL</Label>
                  <Input
                    id='image'
                    value={formData.image}
                    onChange={(e) => handleInputChange('image', e.target.value)}
                    placeholder='Enter image URL'
                    type='url'
                  />
                </div>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='description'>Description *</Label>
                <Textarea
                  id='description'
                  value={formData.description}
                  onChange={(e) =>
                    handleInputChange('description', e.target.value)
                  }
                  placeholder='Enter section description'
                  rows={4}
                  required
                />
              </div>

              <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                <div className='space-y-2'>
                  <Label htmlFor='buttonText'>Button Text</Label>
                  <Input
                    id='buttonText'
                    value={formData.buttonText}
                    onChange={(e) =>
                      handleInputChange('buttonText', e.target.value)
                    }
                    placeholder='Enter button text'
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='buttonLink'>Button Link</Label>
                  <Input
                    id='buttonLink'
                    value={formData.buttonLink}
                    onChange={(e) =>
                      handleInputChange('buttonLink', e.target.value)
                    }
                    placeholder='Enter button link URL'
                    type='url'
                  />
                </div>
              </div>
            </div>
          </ScrollArea>

          <DialogFooter className='mt-6'>
            <Button
              type='button'
              variant='outline'
              onClick={onClose}
              disabled={isPending}
            >
              Cancel
            </Button>
            <Button
              type='submit'
              disabled={
                isPending ||
                !formData.heading.trim() ||
                !formData.description.trim()
              }
            >
              {isPending ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
