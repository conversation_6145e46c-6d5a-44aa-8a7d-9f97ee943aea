'use client';

import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useUpdateAppSection } from '@/hooks/useMutation';
import { IAppSection } from '@/types/query.types';

interface AppSectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  data?: IAppSection;
}

interface FormData {
  heading: string;
  description: string;
  appStoreLink: string;
  playStoreLink: string;
  appImage: string;
}

export function AppSectionModal({
  isOpen,
  onClose,
  data
}: AppSectionModalProps) {
  const [formData, setFormData] = useState<FormData>({
    heading: '',
    description: '',
    appStoreLink: '',
    playStoreLink: '',
    appImage: ''
  });

  const { mutate: updateSection, isPending } = useUpdateAppSection({
    onSuccess: () => {
      onClose();
    }
  });

  useEffect(() => {
    if (data) {
      setFormData({
        heading: data.heading || '',
        description: data.description || '',
        appStoreLink: data.appStoreLink || '',
        playStoreLink: data.playStoreLink || '',
        appImage: data.appImage || ''
      });
    }
  }, [data]);

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.heading.trim() || !formData.description.trim()) {
      return;
    }

    updateSection({
      heading: formData.heading.trim(),
      description: formData.description.trim(),
      appStoreLink: formData.appStoreLink.trim(),
      playStoreLink: formData.playStoreLink.trim(),
      appImage: formData.appImage.trim()
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-h-[90vh] max-w-2xl'>
        <DialogHeader>
          <DialogTitle>Edit App Section</DialogTitle>
          <DialogDescription>
            Update the mobile app section content including heading,
            description, store links, and app image.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <ScrollArea className='max-h-[60vh] pr-4'>
            <div className='space-y-6'>
              <div className='space-y-2'>
                <Label htmlFor='heading'>Heading *</Label>
                <Input
                  id='heading'
                  value={formData.heading}
                  onChange={(e) => handleInputChange('heading', e.target.value)}
                  placeholder='Enter section heading'
                  required
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='description'>Description *</Label>
                <Textarea
                  id='description'
                  value={formData.description}
                  onChange={(e) =>
                    handleInputChange('description', e.target.value)
                  }
                  placeholder='Enter section description'
                  rows={4}
                  required
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='appImage'>App Image URL</Label>
                <Input
                  id='appImage'
                  value={formData.appImage}
                  onChange={(e) =>
                    handleInputChange('appImage', e.target.value)
                  }
                  placeholder='Enter app image URL'
                  type='url'
                />
              </div>

              <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                <div className='space-y-2'>
                  <Label htmlFor='appStoreLink'>App Store Link</Label>
                  <Input
                    id='appStoreLink'
                    value={formData.appStoreLink}
                    onChange={(e) =>
                      handleInputChange('appStoreLink', e.target.value)
                    }
                    placeholder='Enter App Store URL'
                    type='url'
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='playStoreLink'>Play Store Link</Label>
                  <Input
                    id='playStoreLink'
                    value={formData.playStoreLink}
                    onChange={(e) =>
                      handleInputChange('playStoreLink', e.target.value)
                    }
                    placeholder='Enter Play Store URL'
                    type='url'
                  />
                </div>
              </div>
            </div>
          </ScrollArea>

          <DialogFooter className='mt-6'>
            <Button
              type='button'
              variant='outline'
              onClick={onClose}
              disabled={isPending}
            >
              Cancel
            </Button>
            <Button
              type='submit'
              disabled={
                isPending ||
                !formData.heading.trim() ||
                !formData.description.trim()
              }
            >
              {isPending ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
