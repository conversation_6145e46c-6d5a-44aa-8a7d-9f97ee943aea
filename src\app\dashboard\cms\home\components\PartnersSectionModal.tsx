'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Plus, Trash2 } from 'lucide-react';
import { useUpdatePartnersSection } from '@/hooks/useMutation';
import { IPartnersSection, IPartner } from '@/types/query.types';

interface PartnersSectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  data?: IPartnersSection;
}

interface FormData {
  heading: string;
  description: string;
  partners: IPartner[];
}

export function PartnersSectionModal({
  isOpen,
  onClose,
  data
}: PartnersSectionModalProps) {
  const [formData, setFormData] = useState<FormData>({
    heading: '',
    description: '',
    partners: []
  });

  const { mutate: updateSection, isPending } = useUpdatePartnersSection({
    onSuccess: () => {
      onClose();
    }
  });

  useEffect(() => {
    if (data) {
      setFormData({
        heading: data.heading || '',
        description: data.description || '',
        partners: data.partners || []
      });
    }
  }, [data]);

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePartnerChange = (
    index: number,
    field: keyof IPartner,
    value: string
  ) => {
    setFormData((prev) => ({
      ...prev,
      partners: prev.partners.map((partner, i) =>
        i === index ? { ...partner, [field]: value } : partner
      )
    }));
  };

  const addPartner = () => {
    setFormData((prev) => ({
      ...prev,
      partners: [...prev.partners, { name: '', logo: '', website: '' }]
    }));
  };

  const removePartner = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      partners: prev.partners.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.heading.trim() || !formData.description.trim()) {
      return;
    }

    // Validate partners
    const validPartners = formData.partners.filter(
      (partner) => partner.name.trim() && partner.logo.trim()
    );

    updateSection({
      heading: formData.heading.trim(),
      description: formData.description.trim(),
      partners: validPartners
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-h-[90vh] max-w-4xl'>
        <DialogHeader>
          <DialogTitle>Edit Partners Section</DialogTitle>
          <DialogDescription>
            Update the partners section content including heading, description,
            and partner information.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <ScrollArea className='max-h-[60vh] pr-4'>
            <div className='space-y-6'>
              <div className='space-y-4'>
                <div className='space-y-2'>
                  <Label htmlFor='heading'>Heading *</Label>
                  <Input
                    id='heading'
                    value={formData.heading}
                    onChange={(e) => handleInputChange('heading', e.target.value)}
                    placeholder='Enter section heading'
                    required
                  />
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='description'>Description *</Label>
                  <Textarea
                    id='description'
                    value={formData.description}
                    onChange={(e) =>
                      handleInputChange('description', e.target.value)
                    }
                    placeholder='Enter section description'
                    rows={3}
                    required
                  />
                </div>
              </div>

              <div className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <Label className='text-base font-medium'>Partners</Label>
                  <Button
                    type='button'
                    variant='outline'
                    size='sm'
                    onClick={addPartner}
                  >
                    <Plus className='mr-1 h-4 w-4' />
                    Add Partner
                  </Button>
                </div>

                <div className='space-y-4'>
                  {formData.partners.map((partner, index) => (
                    <Card key={index} className='border-l-4 border-l-pink-500'>
                      <CardHeader className='pb-3'>
                        <div className='flex items-center justify-between'>
                          <CardTitle className='text-sm'>
                            Partner {index + 1}
                          </CardTitle>
                          <Button
                            type='button'
                            variant='ghost'
                            size='sm'
                            onClick={() => removePartner(index)}
                            className='text-destructive hover:text-destructive'
                          >
                            <Trash2 className='h-4 w-4' />
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent className='space-y-3'>
                        <div className='grid grid-cols-1 gap-3 md:grid-cols-2'>
                          <div className='space-y-2'>
                            <Label htmlFor={`partner-name-${index}`}>
                              Partner Name *
                            </Label>
                            <Input
                              id={`partner-name-${index}`}
                              value={partner.name}
                              onChange={(e) =>
                                handlePartnerChange(index, 'name', e.target.value)
                              }
                              placeholder='Enter partner name'
                            />
                          </div>
                          <div className='space-y-2'>
                            <Label htmlFor={`partner-logo-${index}`}>
                              Logo URL *
                            </Label>
                            <Input
                              id={`partner-logo-${index}`}
                              value={partner.logo}
                              onChange={(e) =>
                                handlePartnerChange(index, 'logo', e.target.value)
                              }
                              placeholder='Enter logo URL'
                              type='url'
                            />
                          </div>
                        </div>
                        <div className='space-y-2'>
                          <Label htmlFor={`partner-website-${index}`}>
                            Website URL (Optional)
                          </Label>
                          <Input
                            id={`partner-website-${index}`}
                            value={partner.website || ''}
                            onChange={(e) =>
                              handlePartnerChange(index, 'website', e.target.value)
                            }
                            placeholder='Enter website URL'
                            type='url'
                          />
                        </div>
                      </CardContent>
                    </Card>
                  ))}

                  {formData.partners.length === 0 && (
                    <div className='text-muted-foreground py-8 text-center'>
                      <p>
                        No partners added yet. Click &quot;Add Partner&quot; to
                        create your first partner.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </ScrollArea>

          <DialogFooter className='mt-6'>
            <Button
              type='button'
              variant='outline'
              onClick={onClose}
              disabled={isPending}
            >
              Cancel
            </Button>
            <Button
              type='submit'
              disabled={
                isPending ||
                !formData.heading.trim() ||
                !formData.description.trim()
              }
            >
              {isPending ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
