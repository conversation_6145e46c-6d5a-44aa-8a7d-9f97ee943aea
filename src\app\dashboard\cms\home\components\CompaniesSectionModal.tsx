'use client';

import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Plus, X } from 'lucide-react';
import { useUpdateCompaniesSection } from '@/hooks/useMutation';
import { ICompaniesSection } from '@/types/query.types';

interface CompaniesSectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  data?: ICompaniesSection;
}

interface FormData {
  heading: string;
  description: string;
  featuredCompanies: string[];
  showAllLink: string;
}

export function CompaniesSectionModal({
  isOpen,
  onClose,
  data
}: CompaniesSectionModalProps) {
  const [formData, setFormData] = useState<FormData>({
    heading: '',
    description: '',
    featuredCompanies: [],
    showAllLink: ''
  });
  const [newCompany, setNewCompany] = useState('');

  const { mutate: updateSection, isPending } = useUpdateCompaniesSection({
    onSuccess: () => {
      onClose();
    }
  });

  useEffect(() => {
    if (data) {
      setFormData({
        heading: data.heading || '',
        description: data.description || '',
        featuredCompanies: data.featuredCompanies || [],
        showAllLink: data.showAllLink || ''
      });
    }
  }, [data]);

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value
    }));
  };

  const addCompany = () => {
    if (newCompany.trim()) {
      setFormData((prev) => ({
        ...prev,
        featuredCompanies: [...prev.featuredCompanies, newCompany.trim()]
      }));
      setNewCompany('');
    }
  };

  const removeCompany = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      featuredCompanies: prev.featuredCompanies.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.heading.trim() || !formData.description.trim()) {
      return;
    }

    updateSection({
      heading: formData.heading.trim(),
      description: formData.description.trim(),
      featuredCompanies: formData.featuredCompanies,
      showAllLink: formData.showAllLink.trim()
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-h-[90vh] max-w-2xl'>
        <DialogHeader>
          <DialogTitle>Edit Companies Section</DialogTitle>
          <DialogDescription>
            Update the companies section content including heading, description,
            featured companies, and show all link.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit}>
          <ScrollArea className='max-h-[60vh] pr-4'>
            <div className='space-y-6'>
              <div className='space-y-2'>
                <Label htmlFor='heading'>Heading *</Label>
                <Input
                  id='heading'
                  value={formData.heading}
                  onChange={(e) => handleInputChange('heading', e.target.value)}
                  placeholder='Enter section heading'
                  required
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='description'>Description *</Label>
                <Textarea
                  id='description'
                  value={formData.description}
                  onChange={(e) =>
                    handleInputChange('description', e.target.value)
                  }
                  placeholder='Enter section description'
                  rows={3}
                  required
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='showAllLink'>Show All Link</Label>
                <Input
                  id='showAllLink'
                  value={formData.showAllLink}
                  onChange={(e) =>
                    handleInputChange('showAllLink', e.target.value)
                  }
                  placeholder='Enter show all companies link'
                  type='url'
                />
              </div>

              <div className='space-y-4'>
                <Label>Featured Companies</Label>
                
                <div className='flex gap-2'>
                  <Input
                    value={newCompany}
                    onChange={(e) => setNewCompany(e.target.value)}
                    placeholder='Enter company name'
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                        addCompany();
                      }
                    }}
                  />
                  <Button type='button' onClick={addCompany} size='sm'>
                    <Plus className='h-4 w-4' />
                  </Button>
                </div>

                <div className='flex flex-wrap gap-2'>
                  {formData.featuredCompanies.map((company, index) => (
                    <Badge
                      key={index}
                      variant='secondary'
                      className='flex items-center gap-1'
                    >
                      {company}
                      <Button
                        type='button'
                        variant='ghost'
                        size='sm'
                        className='h-auto p-0 text-muted-foreground hover:text-destructive'
                        onClick={() => removeCompany(index)}
                      >
                        <X className='h-3 w-3' />
                      </Button>
                    </Badge>
                  ))}
                </div>

                {formData.featuredCompanies.length === 0 && (
                  <p className='text-muted-foreground text-sm'>
                    No featured companies added yet.
                  </p>
                )}
              </div>
            </div>
          </ScrollArea>

          <DialogFooter className='mt-6'>
            <Button
              type='button'
              variant='outline'
              onClick={onClose}
              disabled={isPending}
            >
              Cancel
            </Button>
            <Button
              type='submit'
              disabled={
                isPending ||
                !formData.heading.trim() ||
                !formData.description.trim()
              }
            >
              {isPending ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
